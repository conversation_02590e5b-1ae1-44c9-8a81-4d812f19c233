### Variables
@baseUrl = {{host}}/api/v1/requests
@ticketId = {{$guid}}
@callId = {{$guid}}
@chatId = {{$guid}}

### Create Ticket with CallId and ChatId
POST {{baseUrl}}/tickets
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "ticketType": 1,
  "subjectId": "{{subjectId}}",
  "customerId": "{{customerId}}",
  "callId": "{{callId}}",
  "chatId": "{{chatId}}",
  "title": "Test Ticket with Call and Chat IDs",
  "description": "This ticket is created with CallId and ChatId for testing purposes",
  "priority": 1,
  "departmentIds": [],
  "ticketFiles": [],
  "watchlist": [],
  "tags": ["test", "call", "chat"]
}

### Get Ticket - Should include CallId and ChatId
GET {{baseUrl}}/tickets/{{ticketId}}
Authorization: Bearer {{token}}

### Update Ticket - Update CallId and ChatId
PUT {{baseUrl}}/tickets/{{ticketId}}
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "id": "{{ticketId}}",
  "ticketType": 1,
  "subjectId": "{{subjectId}}",
  "callId": "{{$guid}}",
  "chatId": "{{$guid}}",
  "title": "Updated Ticket with new Call and Chat IDs",
  "description": "Updated description with new CallId and ChatId",
  "priority": 2,
  "departmentIds": [],
  "ticketFiles": [],
  "watchlist": [],
  "tags": ["updated", "call", "chat"]
}

### List Tickets - Filter by CallId
GET {{baseUrl}}/tickets?callId={{callId}}&pageNumber=1&pageSize=10
Authorization: Bearer {{token}}

### List Tickets - Filter by ChatId
GET {{baseUrl}}/tickets?chatId={{chatId}}&pageNumber=1&pageSize=10
Authorization: Bearer {{token}}

### List Tickets - Filter by both CallId and ChatId
GET {{baseUrl}}/tickets?callId={{callId}}&chatId={{chatId}}&pageNumber=1&pageSize=10
Authorization: Bearer {{token}}
