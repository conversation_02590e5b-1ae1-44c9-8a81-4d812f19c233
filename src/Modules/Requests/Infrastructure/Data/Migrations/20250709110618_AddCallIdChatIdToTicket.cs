﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Requests.Infrastructure.Data.Migrations
{
    /// <inheritdoc />
    public partial class AddCallIdChatIdToTicket : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "CallId",
                schema: "Requests",
                table: "Tickets",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "ChatId",
                schema: "Requests",
                table: "Tickets",
                type: "uniqueidentifier",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CallId",
                schema: "Requests",
                table: "Tickets");

            migrationBuilder.DropColumn(
                name: "<PERSON>t<PERSON><PERSON>",
                schema: "Requests",
                table: "Tickets");
        }
    }
}
