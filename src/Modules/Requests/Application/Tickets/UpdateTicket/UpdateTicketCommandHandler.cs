using MediatR;
using Microsoft.EntityFrameworkCore;
using Requests.Application.Abstractions;
using Requests.Domain;
using Shared.Application;
using Shared.Contracts;

namespace Requests.Application.Tickets.UpdateTicket;

public class UpdateTicketCommandHandler(
    IRequestsDbContext dbContext,
    ISharedDepartmentService departmentService,
    ITicketHistoryService historyService
) : IRequestHandler<UpdateTicketCommand, Result>
{
    private readonly IRequestsDbContext _dbContext = dbContext;
    private readonly ISharedDepartmentService _departmentService = departmentService;
    private readonly ITicketHistoryService _historyService = historyService;

    public async Task<Result> Handle(UpdateTicketCommand request, CancellationToken cancellationToken)
    {
        var ticket = await _dbContext.Tickets
            .Include(t => t.TicketFiles)
            .FirstOrDefaultAsync(t => t.Id == request.Id, cancellationToken);

        if (ticket == null)
        {
            return Result.Failure("Ticket.NotFound", "Ticket bulunamadı.");
        }

        // Eski değerleri sakla (history tracking için)
        var oldTicket = new Ticket
        {
            Id = ticket.Id,
            Title = ticket.Title,
            Description = ticket.Description,
            Priority = ticket.Priority,
            EndDate = ticket.EndDate,
            NotificationWayId = ticket.NotificationWayId,
            SubjectId = ticket.SubjectId,
            TicketType = ticket.TicketType,
            TopTicketId = ticket.TopTicketId,
            CallId = ticket.CallId,
            ChatId = ticket.ChatId,
            UserId = ticket.UserId,
            Watchlist = ticket.Watchlist?.ToList() ?? [],
            Tags = ticket.Tags?.ToList() ?? [],
            AttributeData = ticket.AttributeData
        };
        var oldDepartmentIds = ticket.TicketDepartment.Select(td => td.DepartmentId).ToList();

        // Ticket bilgilerini güncelle
        ticket.TicketType = request.TicketType;
        ticket.TopTicketId = request.TopTicketId;
        ticket.SubjectId = request.SubjectId;
        ticket.CallId = request.CallId;
        ticket.ChatId = request.ChatId;
        ticket.Title = request.Title;
        ticket.Description = request.Description;
        ticket.NotificationWayId = request.NotificationWayId;
        ticket.UserId = request.UserId;
        ticket.Priority = request.Priority;
        ticket.StatusId = Guid.Empty;
        ticket.EndDate = request.EndDate;
        ticket.Watchlist = request.Watchlist ?? [];
        ticket.Tags = request.Tags;
        ticket.AttributeData = request.AttributeData;

        // TicketFiles ilişkilerini güncelle
        // Mevcut dosyaları sil
        var filesToRemove = ticket.TicketFiles.ToList();
        foreach (var file in filesToRemove)
        {
            _dbContext.TicketFiles.Remove(file);
        }

        // Yeni dosyaları ekle
        if (request.TicketFiles.Count > 0)
        {
            var newFiles = request.TicketFiles.Select(f => new TicketFile
            {
                Id = f.Id ?? Guid.NewGuid(),
                TicketId = ticket.Id,
                FileId = f.FileId,
                FileName = f.FileName,
                FilePath = f.FilePath
            }).ToList();

            foreach (var file in newFiles)
            {
                _dbContext.TicketFiles.Add(file);
            }
        }

        // Departman ilişkilerini güncelle
        var departmentsToRemove = ticket.TicketDepartment
            .Where(td => !request.DepartmentIds.Contains(td.DepartmentId))
            .ToList();
        foreach (var department in departmentsToRemove)
        {
            _dbContext.TicketDepartments.Remove(department);
        }
        var existingDepartmentIds = ticket.TicketDepartment.Select(td => td.DepartmentId).ToList();
        var newDepartmentIds = request.DepartmentIds.Where(id => !existingDepartmentIds.Contains(id)).ToList();
        var departments = (await _departmentService.GetDepartmentsByIdsAsync(newDepartmentIds)).ToDictionary(x => x.Id, x => x.Name);
        foreach (var departmentId in newDepartmentIds)
        {
            _dbContext.TicketDepartments.Add(new TicketDepartment
            {
                Id = Guid.NewGuid(),
                TicketId = ticket.Id,
                DepartmentId = departmentId,
                DepartmentName = departments.TryGetValue(departmentId, out string? value) ? value : "Bilinmeyen Departman",
            });
        }

        // History tracking
        await _historyService.TrackTicketUpdatedAsync(ticket.Id, oldTicket, ticket, cancellationToken);
        await _historyService.TrackTicketDepartmentChangedAsync(ticket.Id, oldDepartmentIds, request.DepartmentIds, cancellationToken);
        await _historyService.TrackTicketWatchlistChangedAsync(ticket.Id, oldTicket.Watchlist, ticket.Watchlist, cancellationToken);
        await _historyService.TrackTicketTagsChangedAsync(ticket.Id, oldTicket.Tags, ticket.Tags, cancellationToken);

        await _dbContext.SaveChangesAsync(cancellationToken);
        return Result.Success();
    }
}
